# 历史记录服务实现 - 完整的服务层和接口

## 1. 服务接口定义

### 1.1 IKnetProductHistoryService接口

```java
package com.knet.goods.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.knet.goods.model.dto.message.ProductHistoryMessage;
import com.knet.goods.model.dto.req.ProductHistoryQueryRequest;
import com.knet.goods.model.dto.resp.ProductHistoryQueryResp;
import com.knet.goods.model.entity.KnetProductHistory;
import com.knet.goods.model.enums.HistoryOperationType;

import java.util.Date;
import java.util.List;

/**
 * 商品历史记录服务接口
 */
public interface IKnetProductHistoryService extends IService<KnetProductHistory> {
    
    /**
     * 保存历史记录（从消息队列调用）
     */
    void saveHistory(ProductHistoryMessage message);
    
    /**
     * 分页查询历史记录
     */
    IPage<ProductHistoryQueryResp> queryHistoryPage(ProductHistoryQueryRequest request);
    
    /**
     * 查询特定商品的历史记录
     */
    List<ProductHistoryQueryResp> queryByListingId(String listingId);
    
    /**
     * 查询特定操作类型的历史记录
     */
    List<ProductHistoryQueryResp> queryByOperationType(HistoryOperationType operationType, 
                                                      Date startTime, Date endTime);
    
    /**
     * 查询特定操作人的历史记录
     */
    List<ProductHistoryQueryResp> queryByOperator(String operator, Date startTime, Date endTime);
    
    /**
     * 获取历史记录统计信息
     */
    Map<String, Object> getHistoryStatistics(Date startTime, Date endTime);
    
    /**
     * 归档历史数据
     */
    int archiveHistoryData(int days);
    
    /**
     * 清理过期历史数据
     */
    int cleanExpiredHistory(int days);
}
```

### 1.2 请求和响应DTO

```java
package com.knet.goods.model.dto.req;

import com.knet.common.base.BaseRequest;
import com.knet.goods.model.enums.HistoryOperationType;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Date;

@Data
@EqualsAndHashCode(callSuper = true)
@Schema(description = "商品历史记录查询请求")
public class ProductHistoryQueryRequest extends BaseRequest {
    
    @Schema(description = "商品标识")
    private String listingId;
    
    @Schema(description = "原始商品ID")
    private Long originalId;
    
    @Schema(description = "操作类型")
    private HistoryOperationType operationType;
    
    @Schema(description = "操作人")
    private String operator;
    
    @Schema(description = "操作来源")
    private String operationSource;
    
    @Schema(description = "开始时间")
    private Date startTime;
    
    @Schema(description = "结束时间")
    private Date endTime;
    
    @Schema(description = "变更字段（模糊匹配）")
    private String changedField;
}
```

```java
package com.knet.goods.model.dto.resp;

import com.knet.goods.model.enums.HistoryOperationType;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;
import java.util.List;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Schema(description = "商品历史记录查询响应")
public class ProductHistoryQueryResp {
    
    @Schema(description = "历史记录ID")
    private Long id;
    
    @Schema(description = "原始商品ID")
    private Long originalId;
    
    @Schema(description = "商品标识")
    private String listingId;
    
    @Schema(description = "操作类型")
    private HistoryOperationType operationType;
    
    @Schema(description = "操作时间")
    private Date operationTime;
    
    @Schema(description = "操作人")
    private String operator;
    
    @Schema(description = "操作来源")
    private String operationSource;
    
    @Schema(description = "变更前数据")
    private String beforeData;
    
    @Schema(description = "变更后数据")
    private String afterData;
    
    @Schema(description = "变更字段列表")
    private List<String> changedFields;
    
    @Schema(description = "操作备注")
    private String remark;
    
    @Schema(description = "创建时间")
    private Date createTime;
}
```

## 2. 服务实现

### 2.1 KnetProductHistoryServiceImpl

```java
package com.knet.goods.service.impl;

import com.alibaba.fastjson2.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.knet.goods.mapper.KnetProductHistoryMapper;
import com.knet.goods.model.dto.message.ProductHistoryMessage;
import com.knet.goods.model.dto.req.ProductHistoryQueryRequest;
import com.knet.goods.model.dto.resp.ProductHistoryQueryResp;
import com.knet.goods.model.entity.KnetProductHistory;
import com.knet.goods.model.enums.HistoryOperationType;
import com.knet.goods.service.IKnetProductHistoryService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;

import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

@Slf4j
@Service
public class KnetProductHistoryServiceImpl 
    extends ServiceImpl<KnetProductHistoryMapper, KnetProductHistory> 
    implements IKnetProductHistoryService {
    
    @Autowired
    private RedisTemplate<String, Object> redisTemplate;
    
    private static final String HISTORY_MESSAGE_KEY_PREFIX = "history:msg:";
    private static final long MESSAGE_EXPIRE_TIME = 24; // 24小时过期
    
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void saveHistory(ProductHistoryMessage message) {
        // 生成消息唯一键用于幂等性检查
        String messageKey = generateMessageKey(message);
        
        // 检查消息是否已处理
        if (isMessageProcessed(messageKey)) {
            log.warn("消息已处理，跳过: listingId={}, operationType={}", 
                    message.getListingId(), message.getOperationType());
            return;
        }
        
        try {
            // 标记消息正在处理
            markMessageProcessing(messageKey);
            
            // 转换并保存历史记录
            KnetProductHistory history = convertToHistory(message);
            save(history);
            
            // 标记消息处理完成
            markMessageProcessed(messageKey);
            
            log.info("历史记录保存成功: listingId={}, operationType={}", 
                    message.getListingId(), message.getOperationType());
                    
        } catch (Exception e) {
            // 清除处理标记
            clearMessageProcessing(messageKey);
            log.error("历史记录保存失败: listingId={}", message.getListingId(), e);
            throw e;
        }
    }
    
    @Override
    public IPage<ProductHistoryQueryResp> queryHistoryPage(ProductHistoryQueryRequest request) {
        Page<KnetProductHistory> page = new Page<>(request.getQueryStartPage(), request.getPageSize());
        
        LambdaQueryWrapper<KnetProductHistory> queryWrapper = buildQueryWrapper(request);
        queryWrapper.orderByDesc(KnetProductHistory::getOperationTime);
        
        IPage<KnetProductHistory> historyPage = page(page, queryWrapper);
        
        return historyPage.convert(this::convertToResp);
    }
    
    @Override
    public List<ProductHistoryQueryResp> queryByListingId(String listingId) {
        LambdaQueryWrapper<KnetProductHistory> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(KnetProductHistory::getListingId, listingId)
                   .orderByDesc(KnetProductHistory::getOperationTime);
        
        List<KnetProductHistory> histories = list(queryWrapper);
        return histories.stream()
                       .map(this::convertToResp)
                       .collect(Collectors.toList());
    }
    
    @Override
    public List<ProductHistoryQueryResp> queryByOperationType(HistoryOperationType operationType, 
                                                            Date startTime, Date endTime) {
        LambdaQueryWrapper<KnetProductHistory> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(KnetProductHistory::getOperationType, operationType);
        
        if (startTime != null) {
            queryWrapper.ge(KnetProductHistory::getOperationTime, startTime);
        }
        if (endTime != null) {
            queryWrapper.le(KnetProductHistory::getOperationTime, endTime);
        }
        
        queryWrapper.orderByDesc(KnetProductHistory::getOperationTime);
        
        List<KnetProductHistory> histories = list(queryWrapper);
        return histories.stream()
                       .map(this::convertToResp)
                       .collect(Collectors.toList());
    }
    
    @Override
    public List<ProductHistoryQueryResp> queryByOperator(String operator, Date startTime, Date endTime) {
        LambdaQueryWrapper<KnetProductHistory> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(KnetProductHistory::getOperator, operator);
        
        if (startTime != null) {
            queryWrapper.ge(KnetProductHistory::getOperationTime, startTime);
        }
        if (endTime != null) {
            queryWrapper.le(KnetProductHistory::getOperationTime, endTime);
        }
        
        queryWrapper.orderByDesc(KnetProductHistory::getOperationTime);
        
        List<KnetProductHistory> histories = list(queryWrapper);
        return histories.stream()
                       .map(this::convertToResp)
                       .collect(Collectors.toList());
    }
    
    @Override
    public Map<String, Object> getHistoryStatistics(Date startTime, Date endTime) {
        return baseMapper.getHistoryStatistics(startTime, endTime);
    }
    
    @Override
    @Transactional(rollbackFor = Exception.class)
    public int archiveHistoryData(int days) {
        Date archiveDate = new Date(System.currentTimeMillis() - days * 24 * 60 * 60 * 1000L);
        return baseMapper.archiveHistoryData(archiveDate);
    }
    
    @Override
    @Transactional(rollbackFor = Exception.class)
    public int cleanExpiredHistory(int days) {
        Date expireDate = new Date(System.currentTimeMillis() - days * 24 * 60 * 60 * 1000L);
        LambdaQueryWrapper<KnetProductHistory> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.lt(KnetProductHistory::getCreateTime, expireDate);
        return baseMapper.delete(queryWrapper);
    }
    
    /**
     * 构建查询条件
     */
    private LambdaQueryWrapper<KnetProductHistory> buildQueryWrapper(ProductHistoryQueryRequest request) {
        LambdaQueryWrapper<KnetProductHistory> queryWrapper = new LambdaQueryWrapper<>();
        
        if (StringUtils.hasText(request.getListingId())) {
            queryWrapper.eq(KnetProductHistory::getListingId, request.getListingId());
        }
        
        if (request.getOriginalId() != null) {
            queryWrapper.eq(KnetProductHistory::getOriginalId, request.getOriginalId());
        }
        
        if (request.getOperationType() != null) {
            queryWrapper.eq(KnetProductHistory::getOperationType, request.getOperationType());
        }
        
        if (StringUtils.hasText(request.getOperator())) {
            queryWrapper.eq(KnetProductHistory::getOperator, request.getOperator());
        }
        
        if (StringUtils.hasText(request.getOperationSource())) {
            queryWrapper.eq(KnetProductHistory::getOperationSource, request.getOperationSource());
        }
        
        if (request.getStartTime() != null) {
            queryWrapper.ge(KnetProductHistory::getOperationTime, request.getStartTime());
        }
        
        if (request.getEndTime() != null) {
            queryWrapper.le(KnetProductHistory::getOperationTime, request.getEndTime());
        }
        
        if (StringUtils.hasText(request.getChangedField())) {
            queryWrapper.like(KnetProductHistory::getChangedFields, request.getChangedField());
        }
        
        return queryWrapper;
    }
    
    /**
     * 转换为响应对象
     */
    private ProductHistoryQueryResp convertToResp(KnetProductHistory history) {
        List<String> changedFields = new ArrayList<>();
        if (StringUtils.hasText(history.getChangedFields())) {
            changedFields = Arrays.asList(history.getChangedFields().split(","));
        }
        
        return ProductHistoryQueryResp.builder()
                .id(history.getId())
                .originalId(history.getOriginalId())
                .listingId(history.getListingId())
                .operationType(history.getOperationType())
                .operationTime(history.getOperationTime())
                .operator(history.getOperator())
                .operationSource(history.getOperationSource())
                .beforeData(history.getBeforeData())
                .afterData(history.getAfterData())
                .changedFields(changedFields)
                .remark(history.getRemark())
                .createTime(history.getCreateTime())
                .build();
    }
    
    /**
     * 消息转换为历史记录实体
     */
    private KnetProductHistory convertToHistory(ProductHistoryMessage message) {
        return KnetProductHistory.builder()
                .originalId(message.getOriginalId())
                .listingId(message.getListingId())
                .operationType(message.getOperationType())
                .operationTime(message.getOperationTime())
                .operator(message.getOperator())
                .operationSource(message.getOperationSource())
                .beforeData(message.getBeforeData())
                .afterData(message.getAfterData())
                .changedFields(message.getChangedFields() != null ? 
                              String.join(",", message.getChangedFields()) : null)
                .remark(message.getRemark())
                .build();
    }
    
    /**
     * 生成消息唯一键
     */
    private String generateMessageKey(ProductHistoryMessage message) {
        return HISTORY_MESSAGE_KEY_PREFIX + 
               message.getListingId() + ":" + 
               message.getOperationType() + ":" + 
               message.getOperationTime().getTime();
    }
    
    /**
     * 检查消息是否已处理
     */
    private boolean isMessageProcessed(String messageKey) {
        return redisTemplate.hasKey(messageKey);
    }
    
    /**
     * 标记消息正在处理
     */
    private void markMessageProcessing(String messageKey) {
        redisTemplate.opsForValue().set(messageKey, "PROCESSING", MESSAGE_EXPIRE_TIME, TimeUnit.HOURS);
    }
    
    /**
     * 标记消息处理完成
     */
    private void markMessageProcessed(String messageKey) {
        redisTemplate.opsForValue().set(messageKey, "PROCESSED", MESSAGE_EXPIRE_TIME, TimeUnit.HOURS);
    }
    
    /**
     * 清除消息处理标记
     */
    private void clearMessageProcessing(String messageKey) {
        redisTemplate.delete(messageKey);
    }
}
```

## 3. 数据访问层

### 3.1 KnetProductHistoryMapper接口

```java
package com.knet.goods.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.knet.goods.model.entity.KnetProductHistory;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.Date;
import java.util.Map;

@Mapper
public interface KnetProductHistoryMapper extends BaseMapper<KnetProductHistory> {
    
    /**
     * 获取历史记录统计信息
     */
    Map<String, Object> getHistoryStatistics(@Param("startTime") Date startTime, 
                                            @Param("endTime") Date endTime);
    
    /**
     * 归档历史数据
     */
    int archiveHistoryData(@Param("archiveDate") Date archiveDate);
    
    /**
     * 创建历史记录表
     */
    void createHistoryTable();
    
    /**
     * 创建归档表
     */
    void createArchiveTable();
}
```

这个服务实现提供了：

1. **完整的CRUD操作**: 支持历史记录的增删改查
2. **灵活的查询功能**: 支持多种条件组合查询
3. **幂等性保证**: 基于Redis的消息去重机制
4. **统计分析功能**: 提供历史数据统计信息
5. **数据归档功能**: 支持历史数据的归档和清理
6. **事务支持**: 确保数据一致性

接下来我将继续完成Mapper XML配置和Controller接口。

