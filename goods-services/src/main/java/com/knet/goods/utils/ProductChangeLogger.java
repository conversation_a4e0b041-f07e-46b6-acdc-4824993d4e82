package com.knet.goods.utils;

import com.knet.common.context.ApiKeyContext;
import com.knet.goods.model.entity.KnetProduct;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.stream.Collectors;

/**
 * KnetProduct变动日志工具类
 * 专门用于记录商品数据变动的详细日志，便于后续追踪和审计
 * 
 * <AUTHOR>
 * @date 2025-08-29
 */
@Slf4j
@Component
public class ProductChangeLogger {
    
    /**
     * 记录商品创建日志
     */
    public static void logProductCreate(KnetProduct product) {
        log.info("🆕 [PRODUCT_CREATE] | 操作人: {} | listingId: {} | oneId: {} | sku: {} | spec: {} | 品牌: {} | 价格: {} | 库存: {} | 仓库: {} | 状态: {} | 标识: {} | 来源: {} | 创建时间: {}", 
                getOperator(), product.getListingId(), product.getOneId(), 
                product.getSku(), product.getSpec(), product.getBrand(), 
                product.getPrice(), product.getStock(), product.getWarehouse(), 
                product.getStatus(), product.getMark(), product.getSource(), new Date());
    }
    
    /**
     * 记录批量商品创建日志
     */
    public static void logBatchProductCreate(List<KnetProduct> products) {
        log.info("🆕 [BATCH_PRODUCT_CREATE] | 操作人: {} | 商品数量: {} | 操作时间: {}", 
                getOperator(), products.size(), new Date());
        
        // 记录前5个商品的详细信息
        for (int i = 0; i < Math.min(products.size(), 5); i++) {
            KnetProduct product = products.get(i);
            log.info("📦 [BATCH_CREATE_DETAIL_{}] | listingId: {} | oneId: {} | sku: {} | spec: {} | 品牌: {} | 价格: {} | 库存: {} | 仓库: {} | 状态: {} | 标识: {} | 来源: {}", 
                    i + 1, product.getListingId(), product.getOneId(), product.getSku(), product.getSpec(), 
                    product.getBrand(), product.getPrice(), product.getStock(), product.getWarehouse(), 
                    product.getStatus(), product.getMark(), product.getSource());
        }
        
        if (products.size() > 5) {
            log.info("📦 [BATCH_CREATE_SUMMARY] | 还有 {} 个商品未显示详情", products.size() - 5);
        }
    }
    
    /**
     * 记录商品价格更新日志
     */
    public static void logPriceUpdate(String listingId, String oneId, String sku, String spec, 
                                    Long oldPrice, Long newPrice, String currency) {
        log.info("💰 [PRICE_UPDATE] | 操作人: {} | listingId: {} | oneId: {} | sku: {} | spec: {} | 价格变更: {} -> {} | 币种: {} | 操作时间: {}", 
                getOperator(), listingId, oneId, sku, spec, oldPrice, newPrice, currency, new Date());
    }
    
    /**
     * 记录商品下架日志
     */
    public static void logProductOffSale(List<KnetProduct> products) {
        log.info("📴 [PRODUCT_OFF_SALE] | 操作人: {} | 下架商品数: {} | 操作时间: {}", 
                getOperator(), products.size(), new Date());
        
        for (KnetProduct product : products) {
            log.info("📦 [OFF_SALE_DETAIL] | listingId: {} | oneId: {} | sku: {} | spec: {} | 品牌: {} | 原价格: {} | 仓库: {} | 原状态: ON_SALE -> OFF_SALE", 
                    product.getListingId(), product.getOneId(), product.getSku(), product.getSpec(), 
                    product.getBrand(), product.getPrice(), product.getWarehouse());
        }
    }
    
    /**
     * 记录商品标识更新日志
     */
    public static void logMarkUpdate(List<String> skus, String targetMark, Map<String, Long> beforeMarkCount) {
        log.info("🏷️ [MARK_UPDATE] | 操作人: {} | SKU数量: {} | 目标标识: {} | 操作时间: {}", 
                getOperator(), skus.size(), targetMark, new Date());
        
        log.info("📊 [MARK_UPDATE_BEFORE] | 变更前标识分布: {}", beforeMarkCount);
        
        // 记录前10个SKU
        for (int i = 0; i < Math.min(skus.size(), 10); i++) {
            log.info("📦 [MARK_UPDATE_DETAIL_{}] | sku: {} | 目标标识: {}", i + 1, skus.get(i), targetMark);
        }
        
        if (skus.size() > 10) {
            log.info("📦 [MARK_UPDATE_SUMMARY] | 还有 {} 个SKU未显示详情", skus.size() - 10);
        }
    }
    
    /**
     * 记录库存锁定日志
     */
    public static void logInventoryLock(String sku, String spec, Long originalPrice, 
                                      int requestCount, int actualCount, List<Long> lockedIds) {
        log.info("🔒 [INVENTORY_LOCK] | 操作人: {} | sku: {} | spec: {} | 原始价格: {} | 请求数量: {} | 实际锁定: {} | 锁定商品ID: {} | 操作时间: {}", 
                getOperator(), sku, spec, originalPrice, requestCount, actualCount, lockedIds, new Date());
    }
    
    /**
     * 记录库存锁定失败日志
     */
    public static void logInventoryLockFailed(String sku, String spec, Long originalPrice, 
                                            int requestCount, int availableCount, String reason) {
        log.error("❌ [INVENTORY_LOCK_FAILED] | 操作人: {} | sku: {} | spec: {} | 原始价格: {} | 请求数量: {} | 可用数量: {} | 失败原因: {} | 操作时间: {}", 
                getOperator(), sku, spec, originalPrice, requestCount, availableCount, reason, new Date());
    }
    
    /**
     * 记录商品信息同步更新日志
     */
    public static void logProductSync(String operation, List<String> affectedSkus, String source) {
        log.info("🔄 [PRODUCT_SYNC] | 操作人: {} | 同步操作: {} | 影响SKU数: {} | 数据源: {} | 操作时间: {}", 
                getOperator(), operation, affectedSkus.size(), source, new Date());
        
        // 记录前5个受影响的SKU
        for (int i = 0; i < Math.min(affectedSkus.size(), 5); i++) {
            log.info("📦 [SYNC_DETAIL_{}] | sku: {} | 操作: {}", i + 1, affectedSkus.get(i), operation);
        }
        
        if (affectedSkus.size() > 5) {
            log.info("📦 [SYNC_SUMMARY] | 还有 {} 个SKU未显示详情", affectedSkus.size() - 5);
        }
    }
    
    /**
     * 记录商品状态变更日志
     */
    public static void logStatusChange(String listingId, String oneId, String sku, String spec, 
                                     String fromStatus, String toStatus, String reason) {
        log.info("🔄 [STATUS_CHANGE] | 操作人: {} | listingId: {} | oneId: {} | sku: {} | spec: {} | 状态变更: {} -> {} | 变更原因: {} | 操作时间: {}", 
                getOperator(), listingId, oneId, sku, spec, fromStatus, toStatus, reason, new Date());
    }
    
    /**
     * 记录商品删除日志
     */
    public static void logProductDelete(List<KnetProduct> products, String reason) {
        log.info("🗑️ [PRODUCT_DELETE] | 操作人: {} | 删除商品数: {} | 删除原因: {} | 操作时间: {}", 
                getOperator(), products.size(), reason, new Date());
        
        for (KnetProduct product : products) {
            log.info("📦 [DELETE_DETAIL] | listingId: {} | oneId: {} | sku: {} | spec: {} | 品牌: {} | 价格: {} | 状态: {} | 删除原因: {}", 
                    product.getListingId(), product.getOneId(), product.getSku(), product.getSpec(), 
                    product.getBrand(), product.getPrice(), product.getStatus(), reason);
        }
    }
    
    /**
     * 记录操作失败日志
     */
    public static void logOperationFailed(String operation, String target, String reason, Exception e) {
        log.error("❌ [OPERATION_FAILED] | 操作人: {} | 操作类型: {} | 操作目标: {} | 失败原因: {} | 异常信息: {} | 操作时间: {}", 
                getOperator(), operation, target, reason, e != null ? e.getMessage() : "无", new Date(), e);
    }
    
    /**
     * 记录操作成功日志
     */
    public static void logOperationSuccess(String operation, String target, String details) {
        log.info("✅ [OPERATION_SUCCESS] | 操作人: {} | 操作类型: {} | 操作目标: {} | 操作详情: {} | 操作时间: {}", 
                getOperator(), operation, target, details, new Date());
    }
    
    /**
     * 获取当前操作人
     */
    private static String getOperator() {
        try {
            String apiKey = ApiKeyContext.getApiKey();
            return apiKey != null ? apiKey : "system";
        } catch (Exception e) {
            return "system";
        }
    }
    
    /**
     * 记录数据变更对比日志
     */
    public static void logDataComparison(String operation, String identifier, 
                                       Map<String, Object> beforeData, Map<String, Object> afterData) {
        log.info("🔍 [DATA_COMPARISON] | 操作人: {} | 操作类型: {} | 标识: {} | 操作时间: {}", 
                getOperator(), operation, identifier, new Date());
        
        if (beforeData != null && !beforeData.isEmpty()) {
            log.info("📋 [BEFORE_DATA] | {}", beforeData);
        }
        
        if (afterData != null && !afterData.isEmpty()) {
            log.info("📋 [AFTER_DATA] | {}", afterData);
        }
        
        // 记录变更的字段
        if (beforeData != null && afterData != null) {
            List<String> changedFields = beforeData.keySet().stream()
                    .filter(key -> !Objects.equals(beforeData.get(key), afterData.get(key)))
                    .collect(Collectors.toList());
            
            if (!changedFields.isEmpty()) {
                log.info("🔄 [CHANGED_FIELDS] | 变更字段: {}", changedFields);
            }
        }
    }
}
